<?php

use App\permitters\PackagePermitter;
use HLS\Deposits\RateAmountStrategyFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\RedirectResponse;

class PackageController extends ResourceBasedController
{
    public function __construct()
    {
        parent::__construct();
        $this->beforeFilter('hotelier');

        /** @var PackagePermitter $permitter */
        $this->permitter = new PackagePermitter();

        View::share('canEdit', $this->canEdit());
        View::share('canDelete', $this->canDelete());

        $this->beforeFilter(function() {
            return $this->permitter->ensure('view');
        }, ['only' => ['index']]);

        $this->beforeFilter(function() {
            return $this->permitter->ensure('edit');
        }, ['only' => ['create', 'store', 'edit', 'update']]);

        $this->beforeFilter(function() {
            return $this->permitter->ensure('delete');
        }, ['only' => ['destroy']]);
    }

    public function canEdit(): bool
    {
        return $this->permitter->can('edit');
    }

    public function canDelete(): bool
    {
        return $this->permitter->can('delete');
    }

    /********************************************************
     * FORM CONTROL                                         *
     ********************************************************/

    public function getCopy(Hotel $hotel, Package $package)
    {
        // Create the new Package
        $copy = $package->replicate()->fill([
            'code' => sprintf('%s-copy-%s', $package->code, substr(md5(mt_rand()), 0, 5)),
        ]);
        $copy->save();

        //Filter out results that are not strings, that are needed to match a table
        $relations = array_filter(
            array_merge(array_keys($package->products->toArray()), ['rates', 'room_types', 'products']),
            fn($key) => is_string($key)
        );

        // Copy it's many-to-many relations
        foreach ($relations as $relation) {
            $table = 'package_'.strtolower($relation);
            $inserts = DB::table($table)
                ->where('package_id', $package->id)
                ->select(DB::raw(sprintf('*, %s as package_id', $copy->id)))
                ->get();
            if (!empty($inserts)) {
                DB::table($table)->insert(json_decode(json_encode($inserts), true));
            }
        }

        return Redirect::to(CachedUrl::package('edit', $copy, $hotel))->with(MESSAGE_SUCCESS, 'Copy Successful. Please remember to change the Name and Code!');
    }

    /**
     * The package search fields
     *
     * @return array
     */
    public function getSearchFields(): array
    {
        return [
            'title' => [
                'rules' => 'min:3',
            ],
            'code' => [
                'rules' => 'min:3',
            ],
            'description' => [
                'rules' => 'min:3',
            ],
            'isEnabled' => [
                'label' => 'Status',
                'type' => 'checkbox',
                'options' => ['Disabled', 'Enabled']
            ],
            'isHidden' => [
                'label' => 'Hidden',
                'type' => 'checkbox',
                'options' => ['No', 'Yes']
            ],
            'numNights' => [
                'label' => 'Number of nights',
                'rules' => ['integer'],
            ],
            'isVoucherRestricted' => [
                'label' => 'Voucher Restricted',
                'type' => 'checkbox',
                'options' => ['Allowed', 'Restricted']
            ],
            'isHiddenRoomTypeRestricted' => [
                'label' => 'Hidden RoomType Restricted',
                'type' => 'checkbox',
                'options' => ['Disabled', 'Enabled']
            ],
            'isStopSellRestricted' => [
                'label' => 'Stop Sell Restricted',
                'type' => 'checkbox',
                'options' => ['Disabled', 'Enabled']
            ],
            'isMinStayRestricted' => [
                'label' => 'Min Stay Restricted',
                'type' => 'checkbox',
                'options' => ['Disabled', 'Enabled']
            ],
            'isArrivalRestricted' => [
                'label' => 'Arrival Restricted',
                'type' => 'checkbox',
                'options' => ['Disabled', 'Enabled']
            ],
            'isDepartureRestricted' => [
                'label' => 'Departure Restricted',
                'type' => 'checkbox',
                'options' => ['Disabled', 'Enabled']
            ],
        ];
    }

    /********************************************************
     * RESOURCE CONTROLLER METHODS                          *
     ********************************************************/

    public function index(Hotel $hotel)
    {
        $packages = $hotel->packages()->getQuery();

        $searcher = new HLS\Searcher($this, [$hotel]);
        $searcher->filter($packages->getQuery());

        $packages = $packages->get();

        return View::make($this->tmpl('index'), [
            'hotel' => $hotel,
            'packages' => $packages,
            'searcher' => $searcher
        ]);
    }

    /**
     * We do not have a show route
     *
     * @return RedirectResponse
     */
    public function show(): RedirectResponse
    {
        Session::reflash();

        return Redirect::to(CachedUrl::package('index', func_get_arg(0))); // 0 = $hotel
    }

    /**
     * Get the day input data from the create and update form
     *
     * @return array
     */
    protected function getDays(): array
    {
        $days = [];

        $fn_getRateFields = function ($tuple) {
            $fields = [];

            foreach (['price', 'points'] as $field) {
                $fields[$field] = strlen(trim($tuple[$field]))
                    ? trim($tuple[$field])
                    : 0;
            }

            $fields['strategy'] = in_array($tuple['strategy'], ['byAmount', 'toAmount', 'byPercentage', 'toPercentage', 'maxAmount', 'minAmount'])
                ? $tuple['strategy']
                : 'byAmount';

            return $fields;
        };

        foreach (Input::get('days', []) as $day) {
            $rate = [];
            $products = [];

            foreach (['room', 'perOccupant'] as $type) {
                foreach ($fn_getRateFields($day[$type]) as $key => $val) {
                    $rate[$type.ucfirst($key)] = $val;
                }
            }

            if (isset($day['products'])) {
                foreach ($day['products'] as $item) {
                    $products[] = array_merge([
                        "product_id" => $item['id']
                    ], $fn_getRateFields($item));
                }
            }

            $days[] = array_filter(compact('rate', 'products')); // array_filter keeps out empty arrays
        }

        return $days;
    }

    /**
     * Get the auto form definition
     *
     * @param null $type
     * @param null $models
     *
     * @return array
     */
    protected function getFormDefinition($type = null, $models = null): array
    {
        // prepare the list if products that could be attached...
        $products = $models['hotel']->products()->orderBy('name', 'asc')->where('appliesTo', '!=', 'booking')->get();
        $productArray = $products->map(function (Product $product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'price' => $product->price,
                'points' => $product->points,
                'per' => $product->appliesTo,
            ];
        });

        // Generate the form array
        $form = [
            'params' => [
                'id' => 'theForm',
            ],
            'fields' => [
                'isEnabled' => [
                    'label' => 'Package Status?',
                    'type' => 'select',
                    'options' => [
                        1 => 'Enabled',
                        0 => 'Disabled',
                    ],
                ],
                'isHidden' => [
                    'label' => 'Visibility?',
                    'type' => 'select',
                    'options' => [
                        0 => 'Visible',
                        1 => 'Hidden - Staff Only',
                    ],
                ],
                'allowHurdles' => [
                    'label' => 'Rate hurdles',
                    'type' => 'select',
                    'options' => [
                        0 => 'Prevent',
                        1 => 'Allow',
                    ],
                ],
                'useRatePurchases' => [
                    'label' => 'Include products from base rate?',
                    'type' => 'select',
                    'options' => [
                        0 => 'No',
                        1 => 'Yes'
                    ],
                ],
                'title' => [
                    'label' => 'Package Title - Displayed when making a booking',
                    'rules' => ['required', 'max:50'],
                ],
                'code' => [
                    'label' => 'Unique Internal Reference Code',
                    'params' => [
                        'placeholder' => 'Something unique to identify this particular package',
                    ],
                    'rules' => ['required', 'max:50', 'slug'],
                ],
                'description' => [
                    'label' => 'Package Description - Displayed when making a booking',
                    'type' => 'textarea',
                    'params' => [
                        'placeholder' => 'Displayed to customers looking to book',
                    ],
                    'rules' => ['required'],
                ],
                'minDate' => [
                    'label' => 'Earliest date this package can be booked? (Optional)',
                    'rules' => ['date_format:Y-m-d'],
                    'params' => [
                        'placeholder' => 'Optional: Leave blank for unlimited',
                        'class' => 'daterange',
                        'minDate' => 0,
                        'maxDate' => '+1Y',
                        'min' => 'input[name=maxDate]',
                    ],
                ],
                'maxDate' => [
                    'label' => 'Latest date this package can be booked? (Optional)',
                    'rules' => array_filter([
                        'date_format:Y-m-d',
                        'before:+2 years',
                        Input::has('minDate') && DateHelper::isDate(Input::get('minDate'))
                            ? sprintf('after:%s', date('Y-m-d', strtotime(Input::get('minDate')) - 86400))
                            : null
                    ]),
                    'params' => [
                        'placeholder' => 'Optional: Leave blank for unlimited',
                        'class' => 'daterange',
                        'minDate' => 0,
                        'maxDate' => '+2Y',
                        'max' => 'input[name=minDate]',
                    ],
                ],
                'minNumDays' => [
                    'label' => 'Minimum number of days in advance this package can be booked',
                    'params' => [
                        'placeholder' => 'Optional: Leave blank for 0',
                    ],
                    'rules' => ['integer'],
                ],
                'roomTypes' => [
                    'label' => 'What room types can this package be booked for?',
                    'type' => 'checkbox',
                    'options' => array_keys_and_vals(
                        $models['hotel']->roomTypes->all(), // need it as an array
                        function ($roomType) {
                            return $roomType->label;
                        },
                        function ($roomType) {
                            return $roomType->id;
                        }
                    ),
                    'selected' => isset($models['package'])
                        ? $models['package']->roomTypes->lists('id')
                        : [],
                    'rules' => 'required', // if none are selected, it won't be in the submission and this will fail
                    'assign' => false, // we'll do this one manually
                ],
                'isHiddenRoomTypeRestricted' => [
                    'label' => 'Can this package be booked for hidden room types?',
                    'type' => 'select',
                    'options' => [
                        0 => 'Allow bookings for hidden room types',
                        1 => 'Prevent bookings for hidden room types',
                    ],
                ],
                'isStopSellRestricted' => [
                    'label' => 'Should this package follow room type stop-sell restrictions?',
                    'type' => 'select',
                    'options' => [
                        1 => 'Follow Stop Sells',
                        0 => 'Ignore Stop Sells',
                    ],
                ],
                'isMinStayRestricted' => [
                    'label' => 'Should this package follow room type minimum stay restrictions?',
                    'type' => 'select',
                    'options' => [
                        0 => 'Ignore Minimum Stays',
                        1 => 'Follow Minimum Stays'
                    ],
                ],
                'isArrivalRestricted' => [
                    'label' => 'Should this package follow room type arrival restrictions?',
                    'type' => 'select',
                    'options' => [
                        0 => 'Ignore Closed To Arrival',
                        1 => 'Follow Closed To Arrival'
                    ],
                    //'selected' => 0,
                ],
                'isDepartureRestricted' => [
                    'label' => 'Should this package follow room type departure restrictions?',
                    'type' => 'select',
                    'options' => [
                        0 => 'Ignore Closed To Departure',
                        1 => 'Follow Closed To Departure'
                    ],
                ],
                'isVoucherRestricted' => [
                    'label' => 'Should this package be subject to booking vouchers?',
                    'type' => 'select',
                    'options' => [
                        0 => 'Allow Vouchers To Discount This Package',
                        1 => 'Prevent Vouchers Discounting This Package',
                    ],
                ],
                'isLockedOnCreate' => [
                    'label' => 'Default reservation lock status?',
                    'type' => 'select',
                    'options' => [
                        1 => 'Locked',
                        0 => 'Unlocked',
                    ],
                ],
                'dateListCsv' => [
                    'label' => 'White/Black List Dates',
                ],
                'isBlackList' => [
                    'label' => 'Are the selected dates black or white listed?',
                    'type' => 'select',
                    'options' => [
                        1 => 'Black List: Package Unavailable On These Dates',
                        0 => 'White List: Package Only Available On These Dates',
                    ],
                    // blacklist by default, because the dates are empty by default, so available for all days
                ],
                'deposit' => [
                    'label' =>
                        $type === self::FORM_TYPE_UPDATE ?
                            '<h1>Deposit</h1>
                             <span class="text-warning">Deposit rules changes will only apply to new reservations. 
                                Existing reservations will not be affected.</span>'
                            :
                            '<h1>Deposit</h1>',
                    'type' => 'text',
                    'params' => [
                        'readonly',
                        'disabled',
                        'style' => 'display:none',
                    ],
                    'assign' => false,
                ],
                'depositThreshold' => [
                    'label' => 'Threshold the price must hit before deposit is required',
                    'type' => 'number',
                    'rules' => [
                        'numeric',
                        'min:0'
                    ],
                    'params' => [
                        'min' => 0
                    ],
                ],
                'depositAmount' => [
                    'label' => 'Amount of deposit required once threshold is reached',
                    'type' => 'number',
                    'rules' => [
                        'numeric',
                        'min:0'
                    ],
                    'params' => [
                        'min' => 0,
                        'step' => '0.01'
                    ],
                ],
                'depositAmountStrategy' => [
                    'label' => 'Strategy to use when applying the deposit amount',
                    'type' => 'select',
                    'options' => [
                        RateAmountStrategyFactory::STRATEGY_PERCENTAGE  => 'Percentage',
                        RateAmountStrategyFactory::STRATEGY_FLAT  => 'Flat Rate'
                    ]
                ],
                'depositTarget' => [
                    'label' => 'Strategy to use when choosing what to apply the rate to',
                    'type' => 'select',
                    'options' => [
                        'first_day' => 'First day',
                        'all_days'  => 'All days'
                    ]
                ],
                'depositTimingRule' => [
                    'label' => 'Collect deposit prior to guest arrival',
                    'type' => 'select',
                    'options' => [
                        null => 'On booking',
                        '0'  => 'On day of arrival',
                        '1'  => '1 day before arrival',
                        '2'  => '2 days before arrival',
                        '3'  => '3 days before arrival',
                        '4'  => '4 days before arrival',
                        '5'  => '5 days before arrival',
                        '6'  => '6 days before arrival',
                        '7'  => '7 days before arrival',
                        '8'  => '8 days before arrival',
                        '9'  => '9 days before arrival',
                        '10'  => '10 days before arrival',
                        '11'  => '11 days before arrival',
                        '12'  => '12 days before arrival',
                        '13'  => '13 days before arrival',
                        '14'  => '14 days before arrival',
                    ]
                ],
                'depositOtaBookings' => [
                    'label' => 'Collect deposits for third-party bookings',
                    'type' => 'select',
                    'options' => [
                        '0' => 'No',
                        '1'  => 'Yes',
                    ],
                    'params' => [
                        'title' => 'For bookings with multiple reservations, one payment will be processed on the earliest auto-payment due date of all eligible reservations within the booking. For single-reservation bookings, payment will be taken based on the rate plan or package configuration.',
                    ],
                ],
                'allProducts' => [
                    'type' => 'hidden',
                    'value' => json_encode($productArray),
                    'assign' => false,
                ],
                'numNights' => [
                    'type' => 'hidden',
                    'rules' => ['required', 'integer', 'min:1'],
                ],
                'days' => [
                    'type' => 'hidden',
                    'value' => json_encode(Input::get('days', [])),
                    'assign' => false,
                    'rules' => 'min:1',
                ],
            ],
        ];

        // Max percentage of deposit required...
        if (Input::get('depositAmountStrategy') == RateAmountStrategyFactory::STRATEGY_PERCENTAGE) {
            $form['fields']['depositAmount']['rules'][] = 'max:100';
        }

        // This is what's submitted back on form error
        if (count(Input::old('days', []))) {
            $form['fields']['days']['value'] = json_encode(Input::old('days'));
            // We need to get rid of Input::old('days') now because the form will try to display it, and crash because it's an array which can't be encoded as a string
            $session = Input::session();
            $session->flashInput(array_except(Input::old(), ['days']));
            $session->ageFlashData();
        } elseif (isset($models['package'])) {
            $days = [];
            foreach ($models['package']->rates->keyBy('night') as $night => $rate) {
                foreach (['room', 'perOccupant'] as $type) {
                    foreach (['price', 'points', 'strategy'] as $field) {
                        $days[$night][$type][$field] = $rate->{$type.ucfirst($field)};
                    }
                }

                $productsForNight = $models['package']->products->filter(function (Product $product) use ($night) {
                    return $product->pivot->night == $night;
                });

                foreach ($productsForNight as $product) {
                    $days[$night]['products'][] = array_merge(
                        ['id' => $product->id],
                        array_only($product->pivot->toArray(), ['price', 'points', 'strategy']),
                    );
                }
            }
            $form['fields']['days']['value'] = json_encode($days);
        }

        switch ($type) {
            case self::FORM_TYPE_CREATE:
                $form['fields']['code']['rules'][] = sprintf('unique:packages,code,NULL,id,hotel_id,%d', $models['hotel']->id);

                break;

            case self::FORM_TYPE_UPDATE:
                $form['fields']['code']['rules'][] = sprintf('unique:packages,code,%d,id,hotel_id,%d', $models['package']->id, $models['hotel']->id);

                break;
        }

        return $form;
    }

    protected function getSaveData($models = [])
    {
        return [
            'hotel_id' => $models['hotel']->id,
        ];
    }

    /**
     * Generate the save success message
     *
     * @param string $type
     * @param array $models
     *
     * @return string
     */
    protected function getSuccessMessage($type, $models): string
    {
        return sprintf('%s package %sd', $models['package']->label, $type);
    }

    /**
     * Get the save success url
     *
     * @param Model $model
     * @param array $models
     *
     * @return string
     */
    protected function getSuccessUrl($model, $models = []): string
    {
        return CachedUrl::index($model);
    }

    /**
     * Does this resource require a password to delete?
     *
     * @return bool
     */
    protected function requiresPasswordToDelete(): bool
    {
        return false;
    }

    /**
     * Called when the package was created
     *
     * @param Package $package
     *
     * @return void
     */
    protected function stored($package)
    {
        $this->sync($package);
    }

    /**
     * Sync the package room types and day data, after being created or updated
     *
     * @param Package $package
     *
     * @return void
     *
     * @see PackageController::stored()
     * @see PackageController::updated()
     */
    protected function sync(Package $package)
    {
        $this->syncRoomTypes($package);
        $this->syncDays($package);
    }

    /**
     * Sync the package day data, after being created or updated
     *
     * @param Package $package
     *
     * @return void
     *
     * @see PackageController::sync()
     * @see PackageController::stored()
     * @see PackageController::updated()
     */
    protected function syncDays(Package $package)
    {
        $days = $this->getDays();
        $package_id = $package->id;
        $rates = [];
        $products = [];

        foreach ($days as $night => $day) {
            $rates[] = array_merge([
                'package_id' => $package_id,
                'night' => $night
            ], $day['rate']);

            foreach ($day['products'] ?? [] as $product) {
                $products[] = array_merge([
                    'package_id' => $package_id,
                    'night' => $night
                ], $product);
            }
        }

        // Save rates
        $table = DB::table("package_rates");
        $table->where('package_id', $package_id)->delete();
        if (!empty($rates)) {
            $table->insert($rates);
        }

        // Save products
        $table = DB::table("package_products");
        $table->where('package_id', $package_id)->delete();
        if (!empty($products)) {
            $table->insert($products);
        }
    }

    /**
     * Sync the package room types, after being created or updated
     *
     * @param Package $package
     *
     * @return void
     *
     * @see PackageController::sync()
     * @see PackageController::stored()
     * @see PackageController::updated()
     */
    protected function syncRoomTypes(Package $package)
    {
        $package->roomTypes()->sync(Input::get('roomTypes'));
    }

    /**
     * Called when the package was updated
     *
     * @param Package $package
     *
     * @return void
     */
    protected function updated($package)
    {
        $this->sync($package);
    }
}
