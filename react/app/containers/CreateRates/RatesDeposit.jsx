import * as React from 'react';
import PropTypes from 'prop-types';
import { FormSet, FormInput, FormSelect } from '@components/Form/index';
import ToggleWithLabel from '@components/inputs/ToggleWithLabel';
import { Field } from 'formik';

const RatesDeposit = ({ isValid, showSaveButton, mode }) => {
    return (
        <FormSet
            title="Deposit"
            isValid={isValid}
            noSaveButton={showSaveButton}
            overflow
            {...(mode === 'edit'
                ? {
                      info:
                          'Deposit rules changes will only apply to new reservations. Existing reservations will not be affected.',
                  }
                : {})}
        >
            <div className="rack-default-layout">
                <Field
                    name="deposit_threshold"
                    label="Threshold the price must hit before deposit is required"
                    component={FormInput}
                />
                <Field
                    name="deposit_amount"
                    label="Amount of deposit required once threshold is reached"
                    component={FormInput}
                />
                <Field
                    key="deposit_strategy"
                    name="deposit_strategy"
                    label="Strategy to use when applying the deposit amount"
                    items={[
                        {
                            name: 'Percentage',
                            id: 'percentage',
                        },
                        {
                            name: 'Flat Rate',
                            id: 'flat',
                        },
                    ]}
                    single
                    component={FormSelect}
                />
                <Field
                    key="deposit_target"
                    name="deposit_target"
                    label="Strategy to use when choosing what to apply the rate to"
                    items={[
                        {
                            name: 'First day',
                            id: 'first_day',
                        },
                        {
                            name: 'All days',
                            id: 'all_days',
                        },
                    ]}
                    single
                    component={FormSelect}
                />
                <Field
                    key="deposit_timing_rule"
                    name="deposit_timing_rule"
                    label="Collect deposit prior to guest arrival"
                    items={[
                        {
                            name: 'On booking',
                            id: 'on_booking',
                        },
                        {
                            name: 'On day of arrival',
                            id: '0',
                        },
                        {
                            name: '1 day before arrival',
                            id: '1',
                        },
                        {
                            name: '2 days before arrival',
                            id: '2',
                        },
                        {
                            name: '3 days before arrival',
                            id: '3',
                        },
                        {
                            name: '4 days before arrival',
                            id: '4',
                        },
                        {
                            name: '5 days before arrival',
                            id: '5',
                        },
                        {
                            name: '6 days before arrival',
                            id: '6',
                        },
                        {
                            name: '7 days before arrival',
                            id: '7',
                        },
                        {
                            name: '8 days before arrival',
                            id: '8',
                        },
                        {
                            name: '9 days before arrival',
                            id: '9',
                        },
                        {
                            name: '10 days before arrival',
                            id: '10',
                        },
                        {
                            name: '11 days before arrival',
                            id: '11',
                        },
                        {
                            name: '12 days before arrival',
                            id: '12',
                        },
                        {
                            name: '13 days before arrival',
                            id: '13',
                        },
                        {
                            name: '14 days before arrival',
                            id: '14',
                        },
                    ]}
                    single
                    tooltip={{
                        message:
                            'For bookings with multiple reservations, ' +
                            'one payment will be processed on the earliest auto-payment due date ' +
                            'of all eligible reservations within the booking. ' +
                            'For single-reservation bookings, payment will be taken based on ' +
                            'the rate plan or package configuration.',
                    }}
                    component={FormSelect}
                />
                <Field
                    key="deposit_ota_bookings"
                    name="deposit_ota_bookings"
                    label="Collect deposits for third-party bookings"
                    items={[
                        {
                            name: 'No',
                            id: 'deposit_ota_bookings_no',
                        },
                        {
                            name: 'Yes',
                            id: 'deposit_ota_bookings_yes',
                        },
                    ]}
                    single
                    component={FormSelect}
                />
            </div>
        </FormSet>
    );
};

RatesDeposit.propTypes = {
    isValid: PropTypes.bool,
    showSaveButton: PropTypes.bool,
    mode: PropTypes.string,
};

export default RatesDeposit;
